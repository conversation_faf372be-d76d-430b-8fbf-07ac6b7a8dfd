<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;">
    <title>Connexion - Système de Gestion</title>
    <link href="./src/css/output.css" rel="stylesheet">
</head>
<body class="bg-gray-100 dark:bg-gray-900 flex items-center justify-center h-screen">
    <div class="w-full max-w-sm">
        <form id="loginForm" class="bg-white dark:bg-gray-800 shadow-xl rounded-lg px-8 pt-6 pb-8">
            <h1 class="text-2xl font-bold text-center mb-6 dark:text-white">Connexion</h1>
            
            <div class="mb-4">
                <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="username">
                    Nom d'utilisateur
                </label>
                <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-gray-200 dark:bg-gray-700 dark:border-gray-600 leading-tight focus:outline-none focus:shadow-outline" id="username" type="text" placeholder="proprietaire" required>
            </div>
            
            <div class="mb-6">
                <label class="block text-gray-700 dark:text-gray-300 text-sm font-bold mb-2" for="password">
                    Mot de passe
                </label>
                <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 dark:text-gray-200 dark:bg-gray-700 dark:border-gray-600 mb-3 leading-tight focus:outline-none focus:shadow-outline" id="password" type="password" placeholder="******************" required>
            </div>

            <div id="errorMessage" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">Nom d'utilisateur ou mot de passe incorrect.</span>
            </div>

            <div class="flex items-center justify-between">
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full" type="submit">
                    Se Connecter
                </button>
            </div>
        </form>
    </div>
    <script src="login.js" defer></script>
</body>
</html>