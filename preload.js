// preload.js - MODIFIÉ POUR L'INTERNATIONALISATION
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('api', {
    // --- Session et Application ---
    session: {
        authenticate: (username, password) => ipcRenderer.invoke('users:authenticate', { username, password }),
        getCurrentUser: () => ipcRenderer.invoke('session:get-current-user'),
    },
    app: {
        getMachineId: () => ipcRenderer.invoke('app:get-machine-id'),
        notifyActivationSuccess: (licenseKey) => ipcRenderer.send('app:activation-success', licenseKey),
        reload: () => ipcRenderer.invoke('app:reload'),
        activateLicense: (licenseKey) => ipcRenderer.invoke('activate-license', licenseKey),
    },
    dialog: {
        openImage: () => ipcRenderer.invoke('dialog:open-image'),
    },
    file: {
        saveImage: (args) => ipcRenderer.invoke('file:save-image', args),
    },

    // --- API Produits ---
    products: {
        getAll: (searchTerm) => ipcRenderer.invoke('products:get-all', searchTerm),
        getById: (id) => ipcRenderer.invoke('products:get-by-id', id),
        add: (product) => ipcRenderer.invoke('products:add', product),
        update: (product) => ipcRenderer.invoke('products:update', product),
        delete: (id) => ipcRenderer.invoke('products:delete', id),
        getCategories: () => ipcRenderer.invoke('products:get-categories'),
        getLowStock: () => ipcRenderer.invoke('products:get-low-stock'),
        adjustStock: (data) => ipcRenderer.invoke('products:adjust-stock', data),
    },

    // --- API Clients ---
    clients: {
        getAll: (searchTerm) => ipcRenderer.invoke('clients:get-all', searchTerm),
        getById: (id) => ipcRenderer.invoke('clients:get-by-id', id),
        add: (client) => ipcRenderer.invoke('clients:add', client),
        update: (client) => ipcRenderer.invoke('clients:update', client),
        delete: (id) => ipcRenderer.invoke('clients:delete', id),
    },

    // --- API Ventes ---
    sales: {
        process: (saleData) => ipcRenderer.invoke('sales:process', saleData),
        getHistory: (filters) => ipcRenderer.invoke('sales:get-history', filters),
        getHistoryForUser: () => ipcRenderer.invoke('sales:get-history-for-user'),
        getDetails: (saleId) => ipcRenderer.invoke('sales:get-details', saleId),
        edit: (editData) => ipcRenderer.invoke('sales:edit', editData),
        getLast: () => ipcRenderer.invoke('sales:get-last'),
        processReturn: (returnData) => ipcRenderer.invoke('sales:process-return', returnData),
    },
    
    // --- API Crédits ---
    credits: {
        getDebtors: () => ipcRenderer.invoke('credits:get-debtors'),
        recordPayment: (paymentData) => ipcRenderer.invoke('credits:record-payment', paymentData),
        addManual: (creditData) => ipcRenderer.invoke('credits:add-manual', creditData),
    },

    // --- API Factures ---
    invoices: {
        getAll: () => ipcRenderer.invoke('invoices:get-all'),
        getDetails: (id) => ipcRenderer.invoke('invoices:get-details', id),
        getNextNumber: () => ipcRenderer.invoke('invoices:get-next-number'),
        create: (invoiceData) => ipcRenderer.invoke('invoices:create', invoiceData),
    },

    // ================== AJOUT DE LA NOUVELLE API I18N ==================
    i18n: {
        getTranslation: (lang) => ipcRenderer.invoke('i18n:get-translation', lang)
    },
    // =================================================================

    // --- API Paramètres ---
    settings: {
        getCompanyInfo: () => ipcRenderer.invoke('settings:get-company-info'),
        saveCompanyInfo: (info) => ipcRenderer.invoke('settings:save-company-info', info),
        language: {
            get: () => ipcRenderer.invoke('settings:get-language'),
            set: (lang) => ipcRenderer.invoke('settings:set-language', lang),
        }
    },

    // --- API Thème ---
    theme: {
        set: (theme) => ipcRenderer.invoke('theme:set', theme),
        get: () => ipcRenderer.invoke('theme:get'),
    },

    // --- API Utilisateurs (Vendeurs) ---
    users: {
        getAll: () => ipcRenderer.invoke('users:get-all'),
        add: (userData) => ipcRenderer.invoke('users:add', userData),
        delete: (id) => ipcRenderer.invoke('users:delete', id),
        updatePassword: (data) => ipcRenderer.invoke('users:update-password', data),
        updateCredentials: (data) => ipcRenderer.invoke('users:update-credentials', data),
    },

    // --- API Dashboard ---
    dashboard: {
        getStats: (range) => ipcRenderer.invoke('dashboard:get-stats', range),
    },

    // --- API Impression ---
    print: {
        toPDF: (htmlContent) => ipcRenderer.invoke('print:to-pdf', htmlContent),
    },
});