// caisse.js - Basé sur la version stable fournie, avec traductions et corrections de bugs.

function debounce(func, delay = 300) {
    let timeoutId;
    return (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, args);
        }, delay);
    };
}

document.addEventListener('DOMContentLoaded', async () => {
    // --- Initialisation de la traduction ---
    await window.i18n.loadTranslations();
    window.i18n.applyTranslationsToHTML();
    const t = window.i18n.t;

    // --- Vérification des API ---
    if (!window.api || !window.api.products || !window.api.clients || !window.api.sales || !window.api.session) {
        document.body.innerHTML = "<h1 class='text-red-500 text-center p-8'>ERREUR CRITIQUE: Une API nécessaire est manquante.</h1>";
        return;
    }

    // --- État de l'application (inchangé) ---
    let cart = [], allProducts = [], categories = [], selectedCategory = 'all', selectedClientId = 1, editMode = false, 
        originalSaleId = null, countdownInterval = null, barcodeBuffer = '', barcodeTimer = null;

    // --- Éléments du DOM (inchangés) ---
    const productSearchInput = document.getElementById('productSearch');
    const categoryFiltersDiv = document.getElementById('category-filters');
    const productGridDiv = document.getElementById('product-grid');
    const clientSearchInput = document.getElementById('clientSearchInput');
    const clientSearchResultsDiv = document.getElementById('clientSearchResults');
    const selectedClientDisplay = document.getElementById('selectedClientDisplay');
    const cartItemsTbody = document.getElementById('cart-items');
    const totalAmountSpan = document.getElementById('total-amount');
    const amountPaidInput = document.getElementById('amount-paid');
    const setTotalBtn = document.getElementById('set-total-btn');
    const creditInfoP = document.getElementById('credit-info');
    const changeInfoP = document.getElementById('change-info');
    const cancelSaleBtn = document.getElementById('cancel-sale-btn');
    const cashPaymentBtn = document.getElementById('cash-payment-btn');
    const creditPaymentBtn = document.getElementById('credit-payment-btn');
    const lastSalePanel = document.getElementById('lastSalePanel');
    const lastSaleIdSpan = document.getElementById('lastSaleId');
    const countdownSpan = document.getElementById('countdownSpan');
    const editSaleBtn = document.getElementById('editSaleBtn');
    const quickAddClientBtn = document.getElementById('quickAddClientBtn');
    const addClientModal = document.getElementById('addClientModal');
    const addClientForm = document.getElementById('addClientForm');
    const cancelAddClientBtn = document.getElementById('cancelAddClientBtn');

    // --- Fonctions (votre logique originale avec traductions) ---

    function renderCart() {
        if (!cartItemsTbody) return;
        cartItemsTbody.innerHTML = '';
        if (cart.length === 0) {
            const tr = document.createElement('tr');
            tr.innerHTML = `<td colspan="5" class="text-center py-8 text-gray-500">${t('cart_is_empty')}</td>`;
            cartItemsTbody.appendChild(tr);
        } else {
            cart.forEach(item => {
                const tr = document.createElement('tr');
                tr.dataset.productId = item.id;
                const retailBtnClass = item.unit === 'retail' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600';
                const wholesaleBtnClass = item.unit === 'wholesale' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600';
                const cartonBtnClass = item.unit === 'carton' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600';
                const isCartonDisabled = item.pieces_per_carton === 0;
                tr.innerHTML = `<td class="px-2 py-2 align-top"><p class="font-semibold text-sm truncate">${item.name}</p><div class="flex items-center gap-1 mt-1"><span class="text-xs mr-1">Tarif:</span><button class="set-price-btn text-xs px-2 py-0.5 rounded ${retailBtnClass}" data-price-type="retail" title="Prix Détail">D</button><button class="set-price-btn text-xs px-2 py-0.5 rounded ${wholesaleBtnClass}" data-price-type="wholesale" title="Prix Gros">G</button><button class="set-price-btn text-xs px-2 py-0.5 rounded ${cartonBtnClass}" data-price-type="carton" title="Prix Carton" ${isCartonDisabled ? 'disabled style="opacity:0.5; cursor:not-allowed;"' : ''}>C</button></div></td><td class="px-2 py-2 align-top"><input type="number" class="quantity-input w-16 text-center font-bold border rounded dark:bg-gray-700 dark:border-gray-600" value="${item.quantity}" min="1" max="${item.stock}"></td><td class="px-2 py-2 align-top"><input type="number" step="0.01" class="price-input w-24 text-center font-bold border rounded dark:bg-gray-700 dark:border-gray-600" value="${item.price.toFixed(2)}"></td><td class="line-total py-2 px-4 text-right font-bold whitespace-nowrap align-top">${(item.quantity * item.price).toFixed(2)}</td><td class="px-2 py-2 align-top"><button class="text-red-500 hover:text-red-700 remove-item-btn font-bold">X</button></td>`;
                cartItemsTbody.appendChild(tr);
            });
        }
        updateTotals();
    }
    
    function addProductToCart(productId) {
        if (cart.length === 0 && !editMode) { hideLastSalePanel(); }
        const product = allProducts.find(p => p.id === productId);
        if (!product || product.stock <= 0) return;
        const unitToAdd = 'retail';
        let existingItem = cart.find(item => item.id === productId && item.unit === unitToAdd);
        if (existingItem) {
            if (existingItem.quantity < product.stock) {
                existingItem.quantity++;
            } else {
                alert(t('stock_max_reached'));
            }
        } else {
            cart.push({ id: product.id, name: product.name, quantity: 1, unit: 'retail', price: product.price_retail, price_retail: product.price_retail, price_wholesale: product.price_wholesale, price_carton: product.price_carton, pieces_per_carton: product.pieces_per_carton, stock: product.stock });
        }
        renderCart();
    }

    function processBarcode(barcode) { if (!barcode || barcode.length <= 3) return; const product = allProducts.find(p => p.barcode === barcode.trim()); if (product) { addProductToCart(product.id); } else { console.log(`Code-barres non trouvé : ${barcode}`); } }
    function handleKeyDown(e) {
        const activeElement = document.activeElement;
        const isTypingInInput = activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'SELECT');

        // Vérifier si une modal est ouverte
        const isModalOpen = addClientModal && !addClientModal.classList.contains('hidden');

        // Ne pas traiter les codes-barres si on tape dans un champ ou si une modal est ouverte
        if (isTypingInInput || isModalOpen) return;

        if (e.key === 'Enter') {
            e.preventDefault();
            processBarcode(barcodeBuffer);
            barcodeBuffer = '';
            return;
        }

        // Ignorer les touches spéciales
        if (e.key.length > 1) return;

        barcodeBuffer += e.key;
        clearTimeout(barcodeTimer);
        barcodeTimer = setTimeout(() => {
            barcodeBuffer = '';
        }, 100);
    }
    function handlePaste(e) {
        const activeElement = document.activeElement;
        const isTypingInInput = activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'SELECT');
        const isModalOpen = addClientModal && !addClientModal.classList.contains('hidden');

        if (isTypingInInput || isModalOpen) return;

        e.preventDefault();
        const pastedText = e.clipboardData ? e.clipboardData.getData('text') : '';
        if (pastedText) {
            processBarcode(pastedText);
        }
    }
    function openAddClientModal() {
        if (addClientModal) {
            addClientModal.classList.replace('hidden', 'flex');
            // Utiliser setTimeout pour s'assurer que la modal est visible avant de donner le focus
            setTimeout(() => {
                const nameInput = document.getElementById('modal_client_name');
                if (nameInput) {
                    nameInput.focus();
                }
            }, 100);
        }
    }
    function closeAddClientModal() {
        if (addClientModal) {
            addClientModal.classList.replace('flex', 'hidden');
            addClientForm.reset();
            // Pas de focus automatique pour éviter les conflits
        }
    }

    async function initPage() {
        if (typeof initializePage === 'function') await initializePage('caisse');
        [categories, allProducts] = await Promise.all([ window.api.products.getCategories(), window.api.products.getAll() ]);
        renderCategories();
        renderProducts();
        renderCart();
        if (productSearchInput) productSearchInput.focus();

        // Nettoyer les anciens événements avant d'en ajouter de nouveaux
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('paste', handlePaste);

        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('paste', handlePaste);
    }
    
    function updateTotals() { const total = cart.reduce((sum, item) => sum + item.quantity * item.price, 0); if (totalAmountSpan) totalAmountSpan.textContent = total.toFixed(2); const amountPaid = parseFloat(amountPaidInput.value) || 0; const balance = amountPaid - total; if (balance >= 0) { if (creditInfoP) creditInfoP.classList.add('hidden'); if (changeInfoP) { changeInfoP.classList.remove('hidden'); changeInfoP.textContent = `${t('change_due')}: ${balance.toFixed(2)} MAD`; } } else { if (creditInfoP) { creditInfoP.classList.remove('hidden'); creditInfoP.textContent = `${t('credit')}: ${(-balance).toFixed(2)} MAD`; } if (changeInfoP) changeInfoP.classList.add('hidden'); } }
    
    function renderProducts() { if (!productGridDiv) return; const searchTerm = productSearchInput.value.toLowerCase(); const productsToDisplay = allProducts.filter(p => { const inStock = p.stock > 0; const inCategory = selectedCategory === 'all' || p.category === selectedCategory; const matchesSearch = p.name.toLowerCase().includes(searchTerm) || (p.barcode && p.barcode.includes(searchTerm)); return inStock && inCategory && matchesSearch; }); productGridDiv.innerHTML = ''; if (productsToDisplay.length === 0) { productGridDiv.innerHTML = `<p class="text-center text-gray-500 mt-8 col-span-full">${t('no_product_found')}</p>`; return; } const grid = document.createElement('div'); grid.className = 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'; productsToDisplay.forEach(p => { const card = document.createElement('div'); card.className = 'border dark:border-gray-700 rounded-lg p-2 flex flex-col text-center cursor-pointer hover:shadow-lg hover:border-blue-500 transition-all add-product-btn'; card.dataset.productId = p.id; const imageSrc = p.image_path ? p.image_path : 'assets/placeholder.png'; card.innerHTML = `<div class="w-full h-24 mb-2 flex items-center justify-center"><img src="${imageSrc}" alt="${p.name}" class="max-w-full max-h-full object-contain"></div><div class="flex-1 flex items-center justify-center min-h-[40px]"><span class="font-bold text-sm">${p.name}</span></div><div class="mt-auto"><span class="text-xs text-gray-500">Stock: ${p.stock}</span><p class="font-semibold text-blue-600">${p.price_retail.toFixed(2)} MAD</p></div>`; grid.appendChild(card); }); productGridDiv.appendChild(grid); }
    
    function renderCategories() { if (!categoryFiltersDiv) return; categoryFiltersDiv.innerHTML = ''; const allButton = document.createElement('button'); allButton.textContent = t('all_categories'); allButton.className = `px-3 py-1 rounded-full text-sm font-semibold transition ${selectedCategory === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700'}`; allButton.dataset.category = 'all'; categoryFiltersDiv.appendChild(allButton); categories.forEach(cat => { const catButton = document.createElement('button'); catButton.textContent = cat; catButton.className = `px-3 py-1 rounded-full text-sm font-semibold transition ${selectedCategory === cat ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-700'}`; catButton.dataset.category = cat; categoryFiltersDiv.appendChild(catButton); }); }
    
    function resetSale() { cart = []; selectedClientId = 1; if(clientSearchInput) clientSearchInput.value = ''; if(clientSearchResultsDiv) clientSearchResultsDiv.classList.add('hidden'); if(selectedClientDisplay) selectedClientDisplay.textContent = t('default_client'); if(amountPaidInput) amountPaidInput.value = ''; if(productSearchInput) productSearchInput.value = ''; selectedCategory = 'all'; if (!editMode) { renderProducts(); } renderCategories(); renderCart(); if(productSearchInput) productSearchInput.focus(); }
    
    function startCountdown(duration) { let timer = duration; countdownInterval = setInterval(() => { const minutes = Math.floor(timer / 60); const seconds = timer % 60; if(countdownSpan) countdownSpan.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`; if (--timer < 0) { clearInterval(countdownInterval); if(editSaleBtn) { editSaleBtn.disabled = true; editSaleBtn.classList.add('opacity-50', 'cursor-not-allowed'); } if(countdownSpan) countdownSpan.textContent = t('expired'); } }, 1000); }
    
    function showLastSalePanel(saleId) { if (countdownInterval) clearInterval(countdownInterval); if(lastSaleIdSpan) lastSaleIdSpan.textContent = `#${saleId}`; originalSaleId = saleId; if(lastSalePanel) lastSalePanel.classList.remove('hidden'); if(editSaleBtn) { editSaleBtn.disabled = false; editSaleBtn.classList.remove('opacity-50', 'cursor-not-allowed'); } startCountdown(300); }
    
    function hideLastSalePanel() { if (countdownInterval) clearInterval(countdownInterval); if(lastSalePanel) lastSalePanel.classList.add('hidden'); }
    
    async function enterEditMode() { if (!originalSaleId) return; try { const saleDetails = await window.api.sales.getDetails(originalSaleId); if (!saleDetails) { alert(t('edit_details_error')); return; } editMode = true; cart = await Promise.all(saleDetails.items.map(async item => { const productInfo = allProducts.find(p => p.id === item.product_id); const originalStock = (productInfo ? productInfo.stock : 0) + item.quantity; return { id: item.product_id, name: item.product_name, quantity: item.quantity, price: item.unit_price, price_retail: productInfo ? productInfo.price_retail : item.unit_price, price_wholesale: productInfo ? productInfo.price_wholesale : item.unit_price, stock: originalStock }; })); selectedClientId = saleDetails.client_id; const client = await window.api.clients.getById(selectedClientId); if(selectedClientDisplay) selectedClientDisplay.textContent = client?.name || t('default_client'); if(amountPaidInput) amountPaidInput.value = saleDetails.amount_paid_cash.toFixed(2); if(cashPaymentBtn) cashPaymentBtn.classList.add('hidden'); if(creditPaymentBtn) { creditPaymentBtn.textContent = t('save_correction_button'); creditPaymentBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700'); creditPaymentBtn.classList.add('bg-orange-500', 'hover:bg-orange-600'); } hideLastSalePanel(); renderCart(); } catch (error) { console.error("Erreur lors du passage en mode édition:", error); alert(t('edit_mode_error')); } }
    
    function exitEditMode() { editMode = false; originalSaleId = null; if(cashPaymentBtn) cashPaymentBtn.classList.remove('hidden'); if(creditPaymentBtn) { creditPaymentBtn.textContent = t('credit_payment_button'); creditPaymentBtn.classList.add('bg-blue-600', 'hover:bg-blue-700'); creditPaymentBtn.classList.remove('bg-orange-500', 'hover:bg-orange-600'); } resetSale(); }
    
    async function processAndValidateSale(isCashOnly = false) { if (cart.length === 0) { alert(t('cart_is_empty_alert')); return; } const total = cart.reduce((sum, item) => sum + item.quantity * item.price, 0); let amountPaidCash = 0; let credit = 0; if (isCashOnly) { amountPaidCash = total; credit = 0; } else { amountPaidCash = parseFloat(amountPaidInput.value) || 0; credit = Math.max(0, total - amountPaidCash); } if (credit > 0 && selectedClientId === 1) { alert(t('credit_for_default_client_error')); return; } const saleCart = cart.map(item => ({ id: item.id, quantity: item.quantity, price: item.price, unit: item.unit, purchase_price: allProducts.find(p => p.id === item.id)?.purchase_price || 0 })); const saleData = { clientId: selectedClientId, cart: saleCart, total, amountPaidCash, credit }; try { let result; if (editMode) { alert(t('edit_sale_unsupported')); return; } else { result = await window.api.sales.process(saleData); } if (result && result.success) { alert(t('sale_processed_success').replace('%s', result.saleId)); resetSale(); showLastSalePanel(result.saleId); allProducts = await window.api.products.getAll(); renderProducts(); if (window.updateStockAlertBadge) window.updateStockAlertBadge(); } else { throw new Error(result.error || t('sale_failed_unknown')); } } catch (error) { console.error(t('validation_error'), error); alert(`Erreur: ${error.message}`); } }

    // --- Écouteurs d'événements (logique originale avec vérifications de sécurité) ---
    if (cartItemsTbody) {
        cartItemsTbody.addEventListener('change', e => {
            if (e.target.classList.contains('quantity-input') || e.target.classList.contains('price-input')) {
                const row = e.target.closest('tr'); if (!row) return;
                const productId = parseInt(row.dataset.productId);
                const activeBtn = row.querySelector('.set-price-btn.bg-blue-600'); if (!activeBtn) return;
                const itemIndex = cart.findIndex(i => i.id === productId && i.unit === activeBtn.dataset.priceType);
                if (itemIndex === -1) return;
                const item = cart[itemIndex];
                const newQuantity = parseInt(row.querySelector('.quantity-input').value);
                if (isNaN(newQuantity) || newQuantity < 1) { item.quantity = 1; } 
                else if (newQuantity > item.stock) { alert(t('stock_max_reached')); item.quantity = item.stock; } 
                else { item.quantity = newQuantity; }
                const newPrice = parseFloat(row.querySelector('.price-input').value);
                if (!isNaN(newPrice) && newPrice >= 0) { item.price = newPrice; }
                renderCart();
            }
        });

        cartItemsTbody.addEventListener('click', e => {
            const row = e.target.closest('tr'); if (!row) return;
            const productId = parseInt(row.dataset.productId);
            const activeBtn = row.querySelector('.set-price-btn.bg-blue-600');
            if (!activeBtn) { if (e.target.classList.contains('remove-item-btn')) { const itemIndex = cart.findIndex(i => i.id === productId); if (itemIndex > -1) cart.splice(itemIndex, 1); renderCart(); } return; }
            const currentUnit = activeBtn.dataset.priceType;
            const itemIndex = cart.findIndex(i => i.id === productId && i.unit === currentUnit);
            if (e.target.classList.contains('remove-item-btn')) {
                if (itemIndex > -1) cart.splice(itemIndex, 1);
                renderCart();
            } else if (e.target.classList.contains('set-price-btn')) {
                if (itemIndex > -1) {
                    const item = cart[itemIndex]; item.unit = e.target.dataset.priceType;
                    if (item.unit === 'retail') item.price = item.price_retail;
                    else if (item.unit === 'wholesale') item.price = item.price_wholesale;
                    else if (item.unit === 'carton') item.price = item.price_carton;
                }
                renderCart();
            }
        });
    }
    
    if (quickAddClientBtn) quickAddClientBtn.addEventListener('click', openAddClientModal);
    if (cancelAddClientBtn) cancelAddClientBtn.addEventListener('click', closeAddClientModal);
    if (addClientForm) addClientForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const clientData = {
            name: document.getElementById('modal_client_name').value.trim(),
            phone: document.getElementById('modal_client_phone').value.trim(),
            ice: document.getElementById('modal_client_ice').value.trim(),
            address: document.getElementById('modal_client_address').value.trim()
        };

        if (!clientData.name) {
            alert("Le nom du client est obligatoire.");
            return;
        }

        try {
            const newClient = await window.api.clients.add(clientData);
            if (newClient && newClient.id) {
                // 1. Fermer la modal IMMÉDIATEMENT
                closeAddClientModal();

                // 2. Mettre à jour la sélection du client
                selectedClientId = newClient.id;
                if (selectedClientDisplay) {
                    selectedClientDisplay.textContent = newClient.name;
                }

                // 3. Message de succès APRÈS fermeture
                alert(`Client '${newClient.name}' ajouté avec succès !`);
            }
        } catch (error) {
            console.error('Erreur lors de l\'ajout du client:', error);
            alert(`L'ajout du client a échoué: ${error.message}`);
        }
    });
    
    if (productSearchInput) productSearchInput.addEventListener('input', debounce(() => renderProducts(), 300));
    if (categoryFiltersDiv) categoryFiltersDiv.addEventListener('click', e => { if (e.target.tagName === 'BUTTON') { selectedCategory = e.target.dataset.category; renderCategories(); renderProducts(); } });
    if (productGridDiv) productGridDiv.addEventListener('click', e => { const card = e.target.closest('.add-product-btn'); if (card) { addProductToCart(parseInt(card.dataset.productId)); } });
    if (amountPaidInput) amountPaidInput.addEventListener('input', updateTotals);
    if (clientSearchInput) clientSearchInput.addEventListener('input', debounce(async () => { const searchTerm = clientSearchInput.value; if (searchTerm.length < 2) { if(clientSearchResultsDiv) clientSearchResultsDiv.classList.add('hidden'); return; } try { const clients = await window.api.clients.getAll(searchTerm); if(clientSearchResultsDiv) clientSearchResultsDiv.innerHTML = ''; if (clients.length > 0) { clients.forEach(c => { const itemDiv = document.createElement('div'); itemDiv.className = 'search-result-item p-2 hover:bg-gray-200 dark:hover:bg-gray-600 cursor-pointer'; itemDiv.textContent = `${c.name} (${c.phone || 'N/A'})`; itemDiv.dataset.clientId = c.id; itemDiv.dataset.clientName = c.name; clientSearchResultsDiv.appendChild(itemDiv); }); clientSearchResultsDiv.classList.remove('hidden'); } else { clientSearchResultsDiv.classList.add('hidden'); } } catch (error) { console.error("Erreur pendant la recherche de client:", error); } }, 300));
    if (clientSearchResultsDiv) clientSearchResultsDiv.addEventListener('click', e => { if (e.target.classList.contains('search-result-item')) { selectedClientId = parseInt(e.target.dataset.clientId); selectedClientDisplay.textContent = e.target.dataset.clientName; clientSearchInput.value = ''; clientSearchResultsDiv.classList.add('hidden'); productSearchInput.focus(); } });
    if (setTotalBtn) setTotalBtn.addEventListener('click', () => { const total = parseFloat(totalAmountSpan.textContent) || 0; amountPaidInput.value = total.toFixed(2); updateTotals(); });
    if (cancelSaleBtn) cancelSaleBtn.addEventListener('click', () => { if (editMode) { exitEditMode(); } else { resetSale(); } hideLastSalePanel(); });
    if (cashPaymentBtn) cashPaymentBtn.addEventListener('click', () => processAndValidateSale(true));
    if (creditPaymentBtn) creditPaymentBtn.addEventListener('click', () => processAndValidateSale(false));
    if (editSaleBtn) editSaleBtn.addEventListener('click', enterEditMode);
    document.addEventListener('click', (e) => { const clientSearchContainer = document.getElementById('clientSearchContainer'); if (clientSearchContainer && !clientSearchContainer.contains(e.target)) { if (clientSearchResultsDiv) clientSearchResultsDiv.classList.add('hidden'); } });

    // Lancement de l'initialisation de la page
    initPage();
});